-- MySQL script to setup database for Chat App with XAMPP
-- Run this in phpMyAdmin or MySQL command line

-- Create the lms database if it doesn't exist
CREATE DATABASE IF NOT EXISTS lms;
USE lms;

-- Create Users table
CREATE TABLE IF NOT EXISTS Users (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(100) NULL,
    LastName VARCHAR(100) NULL,
    Account VARCHAR(100) NULL,
    Password VARCHAR(255) NULL,
    Status BOOLEAN NOT NULL DEFAULT FALSE,
    Photo LONGBLOB NULL,
    Color VARCHAR(50) NULL
);

-- Create Chats table
CREATE TABLE IF NOT EXISTS Chats (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    User1Id INT NOT NULL,
    User2Id INT NOT NULL,
    Seen1 BOOLEAN NOT NULL DEFAULT FALSE,
    Seen2 BOOLEAN NOT NULL DEFAULT FALSE,
    <PERSON><PERSON><PERSON><PERSON><PERSON>EY (User1Id) REFERENCES Users(Id),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (User2Id) REFERENCES Users(Id)
);

-- Create Messages table
CREATE TABLE IF NOT EXISTS Messages (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    UserId INT NOT NULL,
    ChatId INT NOT NULL,
    Text VARCHAR(1000) NULL,
    Date DATETIME NULL,
    Status BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (UserId) REFERENCES Users(Id),
    FOREIGN KEY (ChatId) REFERENCES Chats(Id) ON DELETE CASCADE
);

-- Insert sample data for testing
INSERT IGNORE INTO Users (Id, Name, LastName, Account, Password, Status, Color) VALUES
(1, 'John', 'Doe', 'john.doe', 'password123', TRUE, '#FF5733'),
(2, 'Jane', 'Smith', 'jane.smith', 'password456', TRUE, '#33FF57'),
(3, 'Bob', 'Johnson', 'bob.johnson', 'password789', TRUE, '#3357FF');

-- Insert sample chat
INSERT IGNORE INTO Chats (Id, User1Id, User2Id, Seen1, Seen2) VALUES
(1, 1, 2, FALSE, FALSE);

-- Insert sample messages
INSERT IGNORE INTO Messages (UserId, ChatId, Text, Date, Status) VALUES
(1, 1, 'Hello Jane!', NOW(), TRUE),
(2, 1, 'Hi John! How are you?', NOW(), TRUE),
(1, 1, 'I am doing great, thanks!', NOW(), TRUE);

SELECT 'Database setup completed successfully!' as Result;
