# PowerShell script to install Chat App locally without certificate verification
Write-Host "Installing Chat App for local development..." -ForegroundColor Green

try {
    # Enable Developer Mode programmatically
    Write-Host "Checking Developer Mode..." -ForegroundColor Yellow
    
    # Path to the MSIX package
    $packagePath = ".\bin\Release\net8.0-windows10.0.19041.0\win10-x64\AppPackages\Chat-App_1.0.0.1_Test\Chat-App_1.0.0.1_x64.msix"
    
    if (Test-Path $packagePath) {
        Write-Host "Found package: $packagePath" -ForegroundColor Green
        
        # Try to install using Add-AppxPackage with AllowUnsigned flag
        Write-Host "Installing package..." -ForegroundColor Yellow
        Add-AppxPackage -Path $packagePath -AllowUnsigned -ForceApplicationShutdown
        
        Write-Host "Chat App installed successfully!" -ForegroundColor Green
        Write-Host "You can now find 'Chat-App' in your Start Menu." -ForegroundColor Cyan
        
        # Try to launch the app
        Write-Host "Attempting to launch the app..." -ForegroundColor Yellow
        Start-Process "shell:AppsFolder\com.companyname.chatapp_1.0.0.1_x64__9zz4h110yvjzm!App"
        
    } else {
        Write-Host "Package not found at: $packagePath" -ForegroundColor Red
        Write-Host "Please make sure you have built the app first using 'dotnet publish'" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "" -ForegroundColor White
    Write-Host "Alternative solutions:" -ForegroundColor Yellow
    Write-Host "1. Enable Developer Mode in Windows Settings > Update & Security > For developers" -ForegroundColor White
    Write-Host "2. Run the executable directly from: .\bin\Release\net8.0-windows10.0.19041.0\win10-x64\publish\Chat-App.exe" -ForegroundColor White
    Write-Host "3. Right-click on the .msix file and select 'Install'" -ForegroundColor White
}

Write-Host "" -ForegroundColor White
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
