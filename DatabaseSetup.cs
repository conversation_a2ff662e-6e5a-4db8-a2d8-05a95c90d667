using System;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace Chat_App.DatabaseSetup
{
    public class DatabaseInitializer
    {
        private readonly string connectionString = "Server=localhost;User Id=root;Password=;TrustServerCertificate=True;";
        private readonly string dbConnectionString = "Server=localhost;Database=lms;User Id=root;Password=;TrustServerCertificate=True;";

        public async Task SetupDatabase()
        {
            try
            {
                Console.WriteLine("Setting up SQL Server database...");
                
                // Create database
                await CreateDatabase();
                
                // Create tables
                await CreateTables();
                
                // Insert sample data
                await InsertSampleData();
                
                Console.WriteLine("Database setup completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting up database: {ex.Message}");
                throw;
            }
        }

        private async Task CreateDatabase()
        {
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();
            
            var createDbCommand = @"
                IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'lms')
                BEGIN
                    CREATE DATABASE lms;
                    PRINT 'Database lms created successfully';
                END
                ELSE
                BEGIN
                    PRINT 'Database lms already exists';
                END";
            
            using var command = new SqlCommand(createDbCommand, connection);
            await command.ExecuteNonQueryAsync();
            Console.WriteLine("Database creation completed.");
        }

        private async Task CreateTables()
        {
            using var connection = new SqlConnection(dbConnectionString);
            await connection.OpenAsync();
            
            // Create Users table
            var createUsersTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                BEGIN
                    CREATE TABLE Users (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        Name nvarchar(100) NULL,
                        LastName nvarchar(100) NULL,
                        Account nvarchar(100) NULL,
                        Password nvarchar(255) NULL,
                        Status bit NOT NULL DEFAULT 0,
                        Photo varbinary(max) NULL,
                        Color nvarchar(50) NULL
                    );
                END";
            
            using var usersCommand = new SqlCommand(createUsersTable, connection);
            await usersCommand.ExecuteNonQueryAsync();
            Console.WriteLine("Users table created.");
            
            // Create Chats table
            var createChatsTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Chats' AND xtype='U')
                BEGIN
                    CREATE TABLE Chats (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        User1Id int NOT NULL,
                        User2Id int NOT NULL,
                        Seen1 bit NOT NULL DEFAULT 0,
                        Seen2 bit NOT NULL DEFAULT 0,
                        FOREIGN KEY (User1Id) REFERENCES Users(Id),
                        FOREIGN KEY (User2Id) REFERENCES Users(Id)
                    );
                END";
            
            using var chatsCommand = new SqlCommand(createChatsTable, connection);
            await chatsCommand.ExecuteNonQueryAsync();
            Console.WriteLine("Chats table created.");
            
            // Create Messages table
            var createMessagesTable = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Messages' AND xtype='U')
                BEGIN
                    CREATE TABLE Messages (
                        Id int IDENTITY(1,1) PRIMARY KEY,
                        UserId int NOT NULL,
                        ChatId int NOT NULL,
                        Text nvarchar(1000) NULL,
                        Date datetime2 NULL,
                        Status bit NOT NULL DEFAULT 0,
                        FOREIGN KEY (UserId) REFERENCES Users(Id),
                        FOREIGN KEY (ChatId) REFERENCES Chats(Id) ON DELETE CASCADE
                    );
                END";
            
            using var messagesCommand = new SqlCommand(createMessagesTable, connection);
            await messagesCommand.ExecuteNonQueryAsync();
            Console.WriteLine("Messages table created.");
        }

        private async Task InsertSampleData()
        {
            using var connection = new SqlConnection(dbConnectionString);
            await connection.OpenAsync();
            
            var insertSampleData = @"
                IF NOT EXISTS (SELECT * FROM Users)
                BEGIN
                    INSERT INTO Users (Name, LastName, Account, Password, Status, Color) VALUES
                    ('John', 'Doe', 'john.doe', 'password123', 1, '#FF5733'),
                    ('Jane', 'Smith', 'jane.smith', 'password456', 1, '#33FF57'),
                    ('Bob', 'Johnson', 'bob.johnson', 'password789', 1, '#3357FF');
                END";
            
            using var command = new SqlCommand(insertSampleData, connection);
            await command.ExecuteNonQueryAsync();
            Console.WriteLine("Sample data inserted.");
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            var initializer = new DatabaseInitializer();
            await initializer.SetupDatabase();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
