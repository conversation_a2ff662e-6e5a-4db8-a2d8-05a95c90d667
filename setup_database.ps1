# PowerShell script to setup SQL Server database for Chat App
Write-Host "Setting up SQL Server database for Chat App..." -ForegroundColor Green

try {
    # Import SQL Server module if available
    if (Get-Module -ListAvailable -Name SqlServer) {
        Import-Module SqlServer
        Write-Host "SqlServer module loaded successfully" -ForegroundColor Green
    } else {
        Write-Host "SqlServer module not found. Trying alternative approach..." -ForegroundColor Yellow
    }

    # Connection string
    $serverName = "localhost"
    $username = "root"
    $password = ""
    
    # SQL commands to create database and tables
    $createDbSql = @"
-- Create the lms database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'lms')
BEGIN
    CREATE DATABASE lms;
    PRINT 'Database lms created successfully';
END
ELSE
BEGIN
    PRINT 'Database lms already exists';
END
"@

    $createTablesSql = @"
USE lms;

-- Create Users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Name nvarchar(100) NULL,
        LastName nvarchar(100) NULL,
        Account nvarchar(100) NULL,
        Password nvarchar(255) NULL,
        Status bit NOT NULL DEFAULT 0,
        Photo varbinary(max) NULL,
        Color nvarchar(50) NULL
    );
    PRINT 'Users table created successfully';
END

-- Create Chats table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Chats' AND xtype='U')
BEGIN
    CREATE TABLE Chats (
        Id int IDENTITY(1,1) PRIMARY KEY,
        User1Id int NOT NULL,
        User2Id int NOT NULL,
        Seen1 bit NOT NULL DEFAULT 0,
        Seen2 bit NOT NULL DEFAULT 0,
        FOREIGN KEY (User1Id) REFERENCES Users(Id),
        FOREIGN KEY (User2Id) REFERENCES Users(Id)
    );
    PRINT 'Chats table created successfully';
END

-- Create Messages table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Messages' AND xtype='U')
BEGIN
    CREATE TABLE Messages (
        Id int IDENTITY(1,1) PRIMARY KEY,
        UserId int NOT NULL,
        ChatId int NOT NULL,
        Text nvarchar(1000) NULL,
        Date datetime2 NULL,
        Status bit NOT NULL DEFAULT 0,
        FOREIGN KEY (UserId) REFERENCES Users(Id),
        FOREIGN KEY (ChatId) REFERENCES Chats(Id) ON DELETE CASCADE
    );
    PRINT 'Messages table created successfully';
END

-- Insert sample data
IF NOT EXISTS (SELECT * FROM Users)
BEGIN
    INSERT INTO Users (Name, LastName, Account, Password, Status, Color) VALUES
    ('John', 'Doe', 'john.doe', 'password123', 1, '#FF5733'),
    ('Jane', 'Smith', 'jane.smith', 'password456', 1, '#33FF57'),
    ('Bob', 'Johnson', 'bob.johnson', 'password789', 1, '#3357FF');
    PRINT 'Sample users inserted successfully';
END

PRINT 'Database setup completed successfully!';
"@

    # Try to execute SQL commands
    if (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue) {
        Write-Host "Using Invoke-Sqlcmd to execute SQL commands..." -ForegroundColor Green
        
        # Create database
        Invoke-Sqlcmd -ServerInstance $serverName -Username $username -Password $password -Query $createDbSql
        
        # Create tables and insert data
        Invoke-Sqlcmd -ServerInstance $serverName -Username $username -Password $password -Query $createTablesSql
        
        Write-Host "Database setup completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Invoke-Sqlcmd not available. Please run the SQL script manually." -ForegroundColor Yellow
        Write-Host "SQL Script saved to create_database.sql" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Error setting up database: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure SQL Server is running and accessible with the provided credentials." -ForegroundColor Yellow
}

Write-Host "Script execution completed." -ForegroundColor Green
